<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/logo.PNG" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#4B0082" />
    <meta
      name="description"
      content="Med Amine Chouchane - Software Developer Portfolio. Showcasing expertise in React.js, Frontend Development, UI/UX Design, and Web Development."
    />
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="Med Amine Chouchane" />
    <meta property="og:description" content="Full-Stack Developer – Angular, React, Node.js, Showcasing expertise in React.js, Frontend Development, UI/UX Design, and Web Development." />
    <meta property="og:image" content="https://porfolio-pro.onrender.com/logo.PNG" />
    <meta property="og:image:secure_url" content="https://porfolio-pro.onrender.com/logo.PNG" />
    <meta property="og:image:type" content="image/png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Med Amine Chouchane Portfolio Logo" />
    <meta property="og:url" content="https://porfolio-pro.onrender.com" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="Med Amine Chouchane" />
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Med Amine Chouchane" />
    <meta name="twitter:description" content="Full-Stack Developer – React, Node.js, and Flutter. Showcasing expertise in React.js, Frontend Development, UI/UX Design, and Web Development." />
    <meta name="twitter:image" content="https://porfolio-pro.onrender.com/logo.PNG" />
    <meta name="twitter:creator" content="@medaminechouchane" />
    <meta name="twitter:site" content="@medaminechouchane" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo.PNG" />

    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Med Amine Chouchane Portfolio</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
